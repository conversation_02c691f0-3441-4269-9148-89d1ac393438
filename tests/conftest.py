import pytest
from fastapi.testclient import TestClient
from app.main import app
from app.shared.config.base import get_settings
from app.autogen_service.head_agent import HeadAgent
from app.tools.tool_loader import ToolLoader


@pytest.fixture
def test_client():
    return TestClient(app)


@pytest.fixture
def settings():
    return get_settings()


@pytest.fixture
def tool_loader():
    return ToolLoader()


@pytest.fixture
def head_agent():
    return HeadAgent()
