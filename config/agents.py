{
    "id": "6e165c36-1c3e-491b-aa35-29227ae487bd",
    "name": "asdfasdfadf",
    "description": "asdfasdfasdfasdfasdf",
    "avatar": "/assets/avatars/avatar-5.svg",
    "owner_id": "91a237fd-0225-4e02-9e9f-805eff073b07",
    "user_ids": ["91a237fd-0225-4e02-9e9f-805eff073b07"],
    "owner_type": "user",
    "template_id": None,
    "template_owner_id": None,
    "is_imported": False,
    "is_bench_employee": False,
    "is_changes_marketplace": False,
    "agent_category": "ai_agent",
    "system_message": "asdfasdfasdfasdf",
    "model_provider": "openai",
    "model_name": None,
    "model_api_key": None,
    "workflow_ids": ["83bd82ba-1c22-4b53-9f7c-5b1fcc813e07"],
    "mcp_server_ids": ["e5db65bd-cbe7-4229-881c-53bf0bf20b7d"],
    "agent_topic_type": "asdfasdfasdf",
    "subscriptions": None,
    "visibility": "public",
    "tags": None,
    "status": "active",
    "department": "engineering",
    "organization_id": None,
    "tone": "friendly",
    "files": [
        "https://storage.googleapis.com/ruh-dev/employee_knowledge/Screenshot%202025-05-15%20at%2012.38.39%E2%80%AFPM.png/**********-Screenshot2025-05-15at12.38.39PM.png",
        "https://storage.googleapis.com/ruh-dev/employee_knowledge/Screenshot%202025-05-12%20at%202.34.45%E2%80%AFPM.png/**********-Screenshot2025-05-12at2.34.45PM.png",
        "https://storage.googleapis.com/ruh-dev/employee_knowledge/Screenshot%202025-05-12%20at%203.43.34%E2%80%AFPM.png/**********-Screenshot2025-05-12at3.43.34PM.png",
        "https://storage.googleapis.com/ruh-dev/employee_knowledge/Screenshot%202025-05-12%20at%2011.19.34%E2%80%AFPM.png/**********-Screenshot2025-05-12at11.19.34PM.png",
    ],
    "urls": [],
    "ruh_credentials": True,
    "created_at": "2025-05-18T18:25:40.538438",
    "updated_at": "2025-05-19T04:30:19.832589",
    "mcps": [
        {
            "id": "e5db65bd-cbe7-4229-881c-53bf0bf20b7d",
            "name": "News MCP Server Test",
            "logo": None,
            "description": "This is news mcp server ",
            "owner_id": "91a237fd-0225-4e02-9e9f-805eff073b07",
            "user_ids": ["string"],
            "owner_type": "user",
            "url": "http://0.0.0.0:8080/sse",
            "url_type": "sse",
            "git_url": "string",
            "visibility": "private",
            "tags": {"additionalProp1": {}},
            "status": "active",
            "created_at": "2025-05-17T12:34:44.799809",
            "updated_at": "2025-05-17T12:34:44.799821",
            "department": "general",
            "mcp_tools_config": {
                "meta": None,
                "nextCursor": None,
                "tools": [
                    {
                        "name": "get-news",
                        "description": "Get the latest news for a given topic",
                        "input_schema": {
                            "type": "object",
                            "properties": {
                                "topic": {
                                    "type": "string",
                                    "description": "Topic to search for",
                                },
                                "page_size": {
                                    "type": "number",
                                    "description": "Number of results to return (1-25)",
                                },
                            },
                            "required": ["topic", "page_size"],
                        },
                        "annotations": None,
                    },
                    {
                        "name": "get-headlines",
                        "description": "Get the latest headlines for a given country",
                        "input_schema": {
                            "type": "object",
                            "properties": {
                                "country": {
                                    "type": "string",
                                    "description": "Country code (2 letters)",
                                },
                                "page_size": {
                                    "type": "number",
                                    "description": "Number of results to return (1-25)",
                                },
                            },
                            "required": ["country", "page_size"],
                        },
                        "annotations": None,
                    },
                ],
            },
        }
    ],
    "workflows": [
        {
            "id": "83bd82ba-1c22-4b53-9f7c-5b1fcc813e07",
            "name": "Nikhil Test-1",
            "description": "This is Nikhil Patil's test workflow",
            "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/e6935f09-0f17-4dfb-bb5c-8a7d474f20a4.json",
            "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/2451986c-1cd4-4a55-a675-df5e7ba41c27.json",
            "start_nodes": [
                {
                    "field": "view_type",
                    "type": "enum",
                    "transition_id": "mcp_generate-stock-image_generate_ai_stock_image-1746195295218",
                    "enum": ["PORTRAIT", "LANDSCAPE"],
                },
                {
                    "field": "voice_id",
                    "type": "string",
                    "transition_id": "mcp_generate-audio_generate_audio-1746195247275",
                },
                {
                    "field": "topic",
                    "type": "string",
                    "transition_id": "mcp_generate-script_script_generate-1746195229309",
                },
                {
                    "field": "script_type",
                    "type": "enum",
                    "transition_id": "mcp_generate-script_script_generate-1746195229309",
                    "enum": ["TOPIC"],
                },
                {
                    "field": "time",
                    "type": "string",
                    "transition_id": "mcp_generate-script_script_generate-1746195229309",
                },
                {
                    "field": "objective",
                    "type": "string",
                    "transition_id": "mcp_generate-script_script_generate-1746195229309",
                },
                {
                    "field": "audience",
                    "type": "string",
                    "transition_id": "mcp_generate-script_script_generate-1746195229309",
                },
                {
                    "field": "gender",
                    "type": "string",
                    "transition_id": "mcp_generate-script_script_generate-1746195229309",
                },
                {
                    "field": "tone",
                    "type": "string",
                    "transition_id": "mcp_generate-script_script_generate-1746195229309",
                },
                {
                    "field": "speakers",
                    "type": "string",
                    "transition_id": "mcp_generate-script_script_generate-1746195229309",
                },
                {
                    "field": "video_type",
                    "type": "enum",
                    "transition_id": "mcp_generate-script_script_generate-1746195229309",
                    "enum": ["SHORT"],
                },
                {
                    "field": "view_type",
                    "type": "enum",
                    "transition_id": "mcp_generate-video_generate_video-1746195322347",
                    "enum": ["PORTRAIT", "LANDSCAPE"],
                },
                {
                    "field": "event_stock_clips",
                    "type": "array",
                    "transition_id": "mcp_generate-video_generate_video-1746195322347",
                },
            ],
            "owner_id": "91a237fd-0225-4e02-9e9f-805eff073b07",
            "user_ids": ["91a237fd-0225-4e02-9e9f-805eff073b07"],
            "owner_type": "user",
            "workflow_template_id": "33b301d0-e40a-4b93-b435-d3f38d5fab45",
            "template_owner_id": "91a237fd-0225-4e02-9e9f-805eff073b07",
            "url": None,
            "is_imported": False,
            "version": "1.0.0",
            "visibility": "public",
            "category": None,
            "tags": None,
            "status": "active",
            "is_changes_marketplace": True,
            "created_at": "2025-05-17T10:02:28.696644",
            "updated_at": "2025-05-17T11:48:28.509280",
        }
    ],
}
