from enum import Enum


# Enum for SSE event types based on chat response structure
class SSEEventType(str, Enum):
    """Server-Sent Events types for real-time communication."""

    # Session management events
    SESSION_INITIALIZED = "session_initialized"

    # Message streaming events
    MESSAGE_STREAM_STARTED = "message_stream_started"
    MESSAGE_STREAMING = "message_streaming"
    MESSAGE_END = "message_end"

    MESSAGE_RESPONSE = "message_response"

    # MCP (Model Context Protocol) events
    MCP_EXECUTION_STARTED = "mcp_execution_started"
    MCP_EXECUTION_ENDED = "mcp_execution_ended"

    # Workflow execution events
    WORKFLOW_EXECUTION_STARTED = "workflow_execution_started"
    WORKFLOW_EXECUTION_STEP = "workflow_execution_step"
    WORKFLOW_EXECUTION_COMPLETED = "workflow_execution_completed"
    WORKFLOW_EXECUTION_FAILED = "workflow_execution_failed"

    # System events
    KEEP_ALIVE = "keep_alive"
    ERROR = "error"
    CONNECTION_CLOSED = "connection_closed"


class AgentType(Enum):
    GLOBAL_AGENT = "global_agent"
    PERSONA_AGENT = "persona_agent"
    USER_AGENT = "user_agent"
    AI_AGENT = "ai_agent"
    HEAD_AGENT = "head_agent"
