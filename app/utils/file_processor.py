"""
File processing utilities for handling multi-modal attachments in AutoGen agents.
Supports images, documents, and other file types for agent chat interactions.
"""

import base64
import logging
import os
import ssl
import tempfile
from io import BytesIO
from typing import List
from urllib.parse import urlparse

import aiohttp
import certifi
import docx2txt
import textract
from autogen_agentchat.messages import MultiModalMessage
from autogen_core import Image as AGImage
from docx import Document
from PIL import Image
from pypdf import PdfReader

from ..schemas.kafka import MessageAttachment

logger = logging.getLogger(__name__)


class FileProcessor:
    """Handles processing of file attachments for multi-modal agent interactions"""

    # Supported image formats
    SUPPORTED_IMAGE_TYPES = {
        "image/jpeg",
        "image/jpg",
        "image/png",
        "image/gif",
        "image/bmp",
        "image/webp",
        "image/tiff",
    }

    # Supported document formats
    SUPPORTED_DOCUMENT_TYPES = {
        "application/pdf",
        "text/plain",
        "text/markdown",
        "application/msword",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "application/vnd.ms-excel",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    }

    def __init__(self):
        self.logger = logger

    def is_supported_file_type(self, file_type: str) -> bool:
        """Check if the file type is supported for processing"""
        return (
            file_type in self.SUPPORTED_IMAGE_TYPES
            or file_type in self.SUPPORTED_DOCUMENT_TYPES
        )

    def is_image_file(self, file_type: str) -> bool:
        """Check if the file is an image"""
        return file_type in self.SUPPORTED_IMAGE_TYPES

    def is_document_file(self, file_type: str) -> bool:
        """Check if the file is a document"""
        return file_type in self.SUPPORTED_DOCUMENT_TYPES

    def decode_base64_file(self, base64_data: str) -> bytes:
        """Decode base64 encoded file data"""
        try:
            # Remove data URL prefix if present (e.g., "data:image/jpeg;base64,")
            if "," in base64_data:
                base64_data = base64_data.split(",", 1)[1]

            return base64.b64decode(base64_data)
        except Exception as e:
            self.logger.error(f"Failed to decode base64 data: {e}")
            raise ValueError(f"Invalid base64 data: {e}")

    async def download_file_from_url(self, file_url: str) -> bytes:
        """Download file content from URL with proper SSL handling"""
        try:
            # Validate URL
            parsed_url = urlparse(file_url)
            if not parsed_url.scheme or not parsed_url.netloc:
                raise ValueError(f"Invalid URL: {file_url}")

            # Create SSL context with proper certificate verification using certifi
            ssl_context = ssl.create_default_context(cafile=certifi.where())

            # Configure timeout
            timeout = aiohttp.ClientTimeout(total=30, connect=10)

            # Set up headers
            headers = {
                "User-Agent": "Mozilla/5.0 (compatible; AutoGen-Agent/1.0)",
                "Accept": "*/*",
                "Accept-Encoding": "gzip, deflate",
                "Connection": "keep-alive",
            }

            # Create connector with SSL context
            connector = aiohttp.TCPConnector(ssl=ssl_context)

            # Download file with proper SSL configuration
            async with aiohttp.ClientSession(
                connector=connector, timeout=timeout, headers=headers
            ) as session:
                async with session.get(file_url) as response:
                    if response.status != 200:
                        raise ValueError(
                            f"Failed to download file: HTTP {response.status}"
                        )

                    content = await response.read()
                    self.logger.info(f"Downloaded {len(content)} bytes from {file_url}")
                    return content

        except ssl.SSLError as ssl_error:
            # Handle SSL errors specifically with fallback option
            self.logger.warning(
                f"SSL verification failed for {file_url}: {ssl_error}. "
                "Attempting download with SSL verification disabled."
            )
            try:
                # Fallback: disable SSL verification (for development only)
                ssl_context = ssl.create_default_context()
                ssl_context.check_hostname = False
                ssl_context.verify_mode = ssl.CERT_NONE

                timeout = aiohttp.ClientTimeout(total=30, connect=10)
                headers = {
                    "User-Agent": "Mozilla/5.0 (compatible; AutoGen-Agent/1.0)",
                    "Accept": "*/*",
                }

                connector = aiohttp.TCPConnector(ssl=ssl_context)

                async with aiohttp.ClientSession(
                    connector=connector, timeout=timeout, headers=headers
                ) as session:
                    async with session.get(file_url) as response:
                        if response.status != 200:
                            raise ValueError(
                                f"Failed to download file: HTTP {response.status}"
                            )

                        content = await response.read()
                        self.logger.info(
                            f"Downloaded {len(content)} bytes from {file_url} "
                            "(SSL verification disabled)"
                        )
                        return content

            except Exception as fallback_error:
                self.logger.error(
                    f"Fallback download also failed for {file_url}: "
                    f"{fallback_error}"
                )
                raise ValueError(
                    f"Failed to download file even with SSL verification "
                    f"disabled: {fallback_error}"
                )

        except Exception as e:
            self.logger.error(f"Failed to download file from {file_url}: {e}")
            raise ValueError(f"Failed to download file: {e}")

    async def get_file_content(self, attachment: MessageAttachment) -> bytes:
        """Get file content from either base64 data or URL"""
        if attachment.file_data:
            return self.decode_base64_file(attachment.file_data)
        elif attachment.file_url:
            return await self.download_file_from_url(attachment.file_url)
        else:
            raise ValueError("Either file_data or file_url must be provided")

    async def process_image_attachment(self, attachment: MessageAttachment) -> Image:
        """Process an image attachment and return an AutoGen Image object"""
        try:
            # Get image data from either base64 or URL
            image_bytes = await self.get_file_content(attachment)

            # Create PIL Image from bytes
            pil_image = Image.open(BytesIO(image_bytes))

            # Create AutoGen Image object
            autogen_image = AGImage(pil_image)

            source = (
                "base64 data" if attachment.file_data else f"URL: {attachment.file_url}"
            )
            self.logger.info(
                f"Successfully processed image: {attachment.file_name} from {source}"
            )
            return autogen_image

        except Exception as e:
            self.logger.error(f"Failed to process image {attachment.file_name}: {e}")
            raise ValueError(f"Failed to process image: {e}")

    async def process_document_attachment(self, attachment: MessageAttachment) -> str:
        """Process a document attachment and return text content"""
        try:
            # Get document data from either base64 or URL
            document_bytes = await self.get_file_content(attachment)

            # Handle text files directly
            if attachment.file_type in ["text/plain", "text/markdown"]:
                content = document_bytes.decode("utf-8")
                source = (
                    "base64 data"
                    if attachment.file_data
                    else f"URL: {attachment.file_url}"
                )
                self.logger.info(
                    f"Successfully processed text document: {attachment.file_name} from {source}"
                )
                return content

            # Handle PDF files
            elif attachment.file_type == "application/pdf":
                return await self._extract_pdf_text(
                    document_bytes, attachment.file_name
                )

            # Handle DOCX files
            elif (
                attachment.file_type
                == "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
            ):
                return await self._extract_docx_text(
                    document_bytes, attachment.file_name
                )

            # Handle legacy DOC files
            elif attachment.file_type == "application/msword":
                return await self._extract_doc_text(
                    document_bytes, attachment.file_name
                )

            # Handle Excel files (basic support)
            elif attachment.file_type in [
                "application/vnd.ms-excel",
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            ]:
                self.logger.warning(
                    f"Excel file processing not fully implemented for {attachment.file_name}"
                )
                return f"[Excel Document: {attachment.file_name} - Content extraction not yet supported]"

            # For other document types, return a placeholder
            else:
                self.logger.warning(
                    f"Document type {attachment.file_type} not fully supported yet"
                )
                return f"[Document: {attachment.file_name} ({attachment.file_type}) - Content extraction not supported]"

        except Exception as e:
            self.logger.error(f"Failed to process document {attachment.file_name}: {e}")
            raise ValueError(f"Failed to process document: {e}")

    async def _extract_pdf_text(self, pdf_bytes: bytes, file_name: str) -> str:
        """Extract text content from PDF bytes"""
        try:
            pdf_reader = PdfReader(BytesIO(pdf_bytes))
            text_content = []

            for page_num, page in enumerate(pdf_reader.pages):
                try:
                    page_text = page.extract_text()
                    if page_text.strip():
                        text_content.append(f"--- Page {page_num + 1} ---\n{page_text}")
                except Exception as page_error:
                    self.logger.warning(
                        f"Failed to extract text from page {page_num + 1} of {file_name}: {page_error}"
                    )
                    continue

            if not text_content:
                return (
                    f"[PDF Document: {file_name} - No extractable text content found]"
                )

            extracted_text = "\n\n".join(text_content)
            self.logger.info(
                f"Successfully extracted text from PDF: {file_name} ({len(extracted_text)} characters)"
            )
            return extracted_text

        except Exception as e:
            self.logger.error(f"Failed to extract text from PDF {file_name}: {e}")
            return f"[PDF Document: {file_name} - Text extraction failed: {str(e)}]"

    async def _extract_docx_text(self, docx_bytes: bytes, file_name: str) -> str:
        """Extract text content from DOCX bytes"""
        try:
            # Save bytes to temporary file for processing
            with tempfile.NamedTemporaryFile(suffix=".docx", delete=False) as temp_file:
                temp_file.write(docx_bytes)
                temp_file_path = temp_file.name

            try:
                # Extract text using docx2txt (simpler and more reliable)
                text_content = docx2txt.process(temp_file_path)

                if not text_content or not text_content.strip():
                    # Fallback to python-docx for more detailed extraction
                    doc = Document(temp_file_path)
                    paragraphs = []
                    for paragraph in doc.paragraphs:
                        if paragraph.text.strip():
                            paragraphs.append(paragraph.text)
                    text_content = "\n".join(paragraphs)

                if not text_content or not text_content.strip():
                    return f"[DOCX Document: {file_name} - No extractable text content found]"

                self.logger.info(
                    f"Successfully extracted text from DOCX: {file_name} ({len(text_content)} characters)"
                )
                return text_content.strip()

            finally:
                # Clean up temporary file
                try:
                    os.unlink(temp_file_path)
                except Exception:
                    pass

        except Exception as e:
            self.logger.error(f"Failed to extract text from DOCX {file_name}: {e}")
            return f"[DOCX Document: {file_name} - Text extraction failed: {str(e)}]"

    async def _extract_doc_text(self, doc_bytes: bytes, file_name: str) -> str:
        """Extract text content from legacy DOC bytes"""
        try:
            # Save bytes to temporary file for processing
            with tempfile.NamedTemporaryFile(suffix=".doc", delete=False) as temp_file:
                temp_file.write(doc_bytes)
                temp_file_path = temp_file.name

            try:
                text_content = None

                # Try to extract text using docx2txt first (it can handle some DOC files)
                try:
                    text_content = docx2txt.process(temp_file_path)
                except Exception as docx2txt_error:
                    self.logger.info(
                        f"docx2txt failed for {file_name}: {docx2txt_error}"
                    )

                # If docx2txt failed or returned empty content, try textract
                if not text_content or not text_content.strip():
                    try:
                        self.logger.info(f"Trying textract for DOC file: {file_name}")
                        text_content = textract.process(temp_file_path).decode("utf-8")
                    except Exception as textract_error:
                        self.logger.warning(
                            f"Textract also failed for {file_name}: {textract_error}"
                        )
                        return f"[DOC Document: {file_name} - Legacy format could not be processed. Consider converting to DOCX]"

                if not text_content or not text_content.strip():
                    self.logger.warning(
                        f"No text content extracted from DOC file {file_name}"
                    )
                    return f"[DOC Document: {file_name} - No extractable text content found]"

                self.logger.info(
                    f"Successfully extracted text from DOC: {file_name} ({len(text_content)} characters)"
                )
                return text_content.strip()

            finally:
                # Clean up temporary file
                try:
                    os.unlink(temp_file_path)
                except Exception:
                    pass

        except Exception as e:
            self.logger.error(f"Failed to extract text from DOC {file_name}: {e}")
            return f"[DOC Document: {file_name} - Text extraction failed: {str(e)}]"

    async def create_multimodal_message(
        self,
        text_content: str,
        attachments: List[MessageAttachment],
        source: str = "user",
    ) -> MultiModalMessage:
        """
        Create a MultiModalMessage from text content and attachments

        Args:
            text_content: The text part of the message
            attachments: List of file attachments
            source: Source of the message (default: "user")

        Returns:
            MultiModalMessage object ready for AutoGen processing
        """
        try:
            # Start with text content
            content_parts = [text_content] if text_content.strip() else []

            # Process each attachment
            for attachment in attachments:
                if not self.is_supported_file_type(attachment.file_type):
                    self.logger.warning(
                        f"Unsupported file type: {attachment.file_type}"
                    )
                    continue

                if self.is_image_file(attachment.file_type):
                    # Process image and add to content
                    image = await self.process_image_attachment(attachment)
                    content_parts.append(image)

                elif self.is_document_file(attachment.file_type):
                    # Process document and add text content
                    doc_content = await self.process_document_attachment(attachment)
                    content_parts.append(
                        f"\n\n--- Content from {attachment.file_name} ---\n{doc_content}\n--- End of {attachment.file_name} ---\n"
                    )

            # Create MultiModalMessage
            multimodal_message = MultiModalMessage(content=content_parts, source=source)

            self.logger.info(
                f"Created MultiModalMessage with {len(content_parts)} content parts"
            )
            return multimodal_message

        except Exception as e:
            self.logger.error(f"Failed to create MultiModalMessage: {e}")
            raise ValueError(f"Failed to create multimodal message: {e}")

    def validate_attachments(self, attachments: List[MessageAttachment]) -> List[str]:
        """
        Validate attachments and return list of validation errors

        Args:
            attachments: List of attachments to validate

        Returns:
            List of validation error messages (empty if all valid)
        """
        errors = []

        for i, attachment in enumerate(attachments):
            # Check file type support
            if not self.is_supported_file_type(attachment.file_type):
                errors.append(
                    f"Attachment {i+1}: Unsupported file type '{attachment.file_type}'"
                )

            # Check file size (limit to 10MB for now)
            max_size = 10 * 1024 * 1024  # 10MB
            if attachment.file_size > max_size:
                errors.append(
                    f"Attachment {i+1}: File size {attachment.file_size} exceeds limit of {max_size} bytes"
                )

            # Check that either file_data or file_url is provided
            if not attachment.file_data and not attachment.file_url:
                errors.append(
                    f"Attachment {i+1}: Either file_data or file_url must be provided"
                )
            elif attachment.file_data and attachment.file_url:
                errors.append(
                    f"Attachment {i+1}: Provide either file_data or file_url, not both"
                )

            # Validate base64 data if provided
            if attachment.file_data:
                try:
                    self.decode_base64_file(attachment.file_data)
                except ValueError as e:
                    errors.append(f"Attachment {i+1}: Invalid base64 data - {e}")

            # Validate URL if provided
            if attachment.file_url:
                try:
                    parsed_url = urlparse(attachment.file_url)
                    if not parsed_url.scheme or not parsed_url.netloc:
                        errors.append(f"Attachment {i+1}: Invalid URL format")
                except Exception as e:
                    errors.append(f"Attachment {i+1}: URL validation error - {e}")

        return errors

    def get_attachment_summary(self, attachments: List[MessageAttachment]) -> str:
        """Generate a summary of attachments for logging/debugging"""
        if not attachments:
            return "No attachments"

        summary_parts = []
        for attachment in attachments:
            size_mb = attachment.file_size / (1024 * 1024)
            summary_parts.append(
                f"{attachment.file_name} ({attachment.file_type}, {size_mb:.2f}MB)"
            )

        return f"{len(attachments)} attachment(s): {', '.join(summary_parts)}"
