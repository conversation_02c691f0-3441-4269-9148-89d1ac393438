from typing import Dict, Any

# Development environment configuration
development_config: Dict[str, Any] = {
    "api": {"host": "0.0.0.0", "port": 6000},
    "workflow": {
        "api_url": "http://localhost:8000",
        "execute_endpoint": "/execute-by-name",
    },
    "agent": {
        "base_url": "http://localhost:5000",
    },
}

# Testing environment configuration
testing_config: Dict[str, Any] = {
    "api": {"host": "0.0.0.0", "port": 6001},
    "workflow": {
        "api_url": "http://test-workflow-api.example.com",
        "execute_endpoint": "/execute-by-name",
    },
    "agent": {
        "base_url": "http://test-agent-api.example.com",
    },
}

# Production environment configuration
production_config: Dict[str, Any] = {
    "api": {"host": "0.0.0.0", "port": 6000},
    "workflow": {
        "api_url": "https://workflow-api.production.com",
        "execute_endpoint": "/execute-by-name",
    },
    "agent": {
        "base_url": "https://agent-api.production.com",
    },
}

# Environment configuration mapping
env_configs = {
    "development": development_config,
    "testing": testing_config,
    "production": production_config,
}
