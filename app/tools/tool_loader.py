import uuid
import json
import logging
import aiohttp
import asyncio
import requests
from functools import partial
from typing import Dict, List, Any, Optional, AsyncGenerator
from typing_extensions import Annotated
from ..schemas.agent_config import AgentTool
from autogen_core.tools import FunctionTool, Tool
from autogen_ext.tools.mcp import SseMcpToolAdapter, SseServerParams
from .streaming_function_tool import StreamingFunctionTool
from .workflow_tool_loader import WorkflowToolLoader

# Configure logger
logger = logging.getLogger(__name__)


class ToolLoader:
    """
    Class to load and create Tool instances from JSON definitions.
    Handles different tool types including generic workflows.
    """

    def __init__(self):
        """Initialize the tool loader."""
        logger.info("Initializing ToolLoader")
        self.workflow_tool_loader = WorkflowToolLoader()
        logger.debug("WorkflowToolLoader initialized")

    async def load_agent_tools(
        self, agent_tools_configs: List[AgentTool]
    ) -> List[Tool]:
        """
        Loads and creates Tool instances for the agent from the tool configurations.

        Args:
            agent_tools_configs: List of tool configuration dictionaries.

        Returns:
            List[Tool]: List of initialized Tool instances.
        """
        agent_tools: List[Tool] = []

        for tool_config in agent_tools_configs:
            try:
                logger.info("Loading tool from definition: %s", tool_config)

                tool_instance = await self.create_tool_from_definition(tool_config)

                if tool_instance:
                    if asyncio.iscoroutine(tool_instance):
                        tool_instance = await tool_instance

                    # Set default name if not present
                    if not hasattr(tool_instance, "name"):
                        tool_instance.name = f"tool_{len(agent_tools)}"

                    agent_tools.append(tool_instance)
                    logger.debug(f"Successfully loaded tool: {tool_instance.name}")

            except Exception as e:
                logger.error(f"Failed to load tool: {e}", exc_info=True)
                continue

        return agent_tools

    async def create_tool_from_definition(
        self, tool_definition: AgentTool
    ) -> Optional[Tool]:
        """
        Creates a Tool instance based on the provided tool definition.

        Args:
            tool_definition: The definition of the tool to create.

        Returns:
            Optional[Tool]: The created Tool instance, or None if creation failed.
        """
        tool_type = tool_definition.tool_type
        logger.info(f"Creating tool of type '{tool_type}'")

        tool_creators = {
            "mcp_sse": self._create_mcp_sse_tool,
            "workflow": self._create_workflow_tool,
        }

        creator = tool_creators.get(tool_type)
        if not creator:
            logger.warning(f"Unsupported tool type: {tool_type}")
            return None

        try:
            return await creator(tool_definition)
        except Exception as e:
            logger.error(
                f"Failed to create tool of type {tool_type}: {e}", exc_info=True
            )
            return None

    async def _create_mcp_sse_tool(
        self, tool_definition: Dict[str, Any]
    ) -> Optional[SseMcpToolAdapter]:
        """Creates an SseMcpToolAdapter instance."""
        mcp_tool_def = tool_definition.get("mcp_tool", {})
        tool_name = mcp_tool_def.get("tool_name")
        server_params_def = mcp_tool_def.get("server_params", {})

        if not all([tool_name, server_params_def]):
            logger.error("Invalid MCP SSE tool definition")
            return None

        try:
            server_params = SseServerParams(**server_params_def)
            adapter = await SseMcpToolAdapter.from_server_params(
                server_params, tool_name
            )
            logger.info(f"Created MCP SSE Tool Adapter: {tool_name}")
            return adapter
        except Exception as e:
            logger.error(f"Failed to create MCP SSE Tool Adapter: {e}", exc_info=True)
            return None

    async def _create_workflow_tool(self, tool_definition: AgentTool) -> Optional[Tool]:
        """Creates a workflow tool instance."""
        workflow_config = tool_definition.workflow
        if not workflow_config:
            logger.error("Missing workflow configuration")
            return None

        return self._create_specific_workflow_tool(
            workflow_config, self.api_endpoints["workflow_requests"]
        )

    async def _execute_workflow_request(
        self, parameters: Dict[str, Any], url: str, workflow_config: Dict[str, Any]
    ) -> AsyncGenerator[str, None]:
        """
        Executes a workflow request and streams the results.

        Args:
            parameters: Dictionary of parameters for the workflow
            url: The target API endpoint URL
            workflow_config: The workflow's configuration

        Yields:
            JSON string responses from the workflow execution
        """
        tool_name = workflow_config.get("workflow_id", "unnamed_workflow")
        logger.info(f"Executing workflow: {tool_name}")

        try:
            # Prepare payload
            final_payload = {
                **workflow_config.get("payload", {}).get("user_payload_template", {}),
                **parameters,
            }

            workflow_request = {
                "workflow_id": workflow_config.get("workflow_id", tool_name),
                "approval": False,
                "payload": final_payload,
            }

            # Send initial request
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    url, json=workflow_request, headers=self.headers
                ) as response:
                    response.raise_for_status()
                    response_data = await response.json()
                    correlation_id = response_data.get("correlationId")

                    if not correlation_id:
                        raise ValueError("No correlation ID received")

                    # Stream results
                    stream_url = f"{self.api_endpoints['stream_base']}/{correlation_id}"
                    async for response in self._stream_workflow_results(
                        session, stream_url
                    ):
                        yield response

        except Exception as e:
            error_msg = f"Workflow execution error: {str(e)}"
            logger.error(error_msg, exc_info=True)
            yield json.dumps({"error": error_msg})

    async def _stream_workflow_results(
        self,
        session: aiohttp.ClientSession,
        stream_url: str,
        keepalive_interval: int = 10,
    ) -> AsyncGenerator[str, None]:
        """
        Streams workflow results from the given URL.

        Args:
            session: aiohttp ClientSession
            stream_url: URL to stream results from
            keepalive_interval: Interval for keepalive messages

        Yields:
            JSON string responses from the stream
        """
        try:
            async with session.get(stream_url, headers=self.headers) as stream_response:
                stream_response.raise_for_status()
                while not stream_response.content.at_eof():
                    try:
                        line = await asyncio.wait_for(
                            stream_response.content.readline(),
                            timeout=keepalive_interval,
                        )

                        if not line:
                            break

                        decoded_line = line.decode("utf-8").strip()
                        if decoded_line.startswith("data:"):
                            data = decoded_line[5:].strip()
                            try:
                                json_data = json.loads(data)
                                yield json.dumps({"stream_data": json_data})
                            except json.JSONDecodeError:
                                yield json.dumps({"stream_raw_data": data})

                    except asyncio.TimeoutError:
                        yield json.dumps({"keepalive": True})
                        logger.debug("Sent keepalive message")

        except Exception as e:
            error_msg = f"Streaming error: {str(e)}"
            logger.error(error_msg, exc_info=True)
            yield json.dumps({"error": error_msg})

    def _create_specific_workflow_tool(
        self, workflow_config: Dict[str, Any], url: str
    ) -> Optional[Tool]:
        """
        Creates a FunctionTool for a specific workflow.

        Args:
            workflow_config: Configuration for the specific workflow
            url: The URL endpoint for this workflow

        Returns:
            Tool: A configured FunctionTool instance
        """
        tool_name = workflow_config.get("workflow_id")
        tool_description = workflow_config.get("description")

        if not all([tool_name, tool_description]):
            logger.error("Invalid workflow configuration")
            return None

        try:
            bound_executor = partial(
                self._execute_workflow_request, url=url, workflow_config=workflow_config
            )

            async def wrapped_executor(
                parameters: Dict[str, Any],
            ) -> AsyncGenerator[str, None]:
                async for item in bound_executor(parameters=parameters):
                    yield item

            required_params = workflow_config.get("payload", {}).get(
                "user_dependent_fields", []
            )
            description = (
                f"{tool_description}\n\nRequired parameters: {required_params}"
            )

            function_tool = StreamingFunctionTool(
                func=wrapped_executor, name=tool_name, description=description
            )

            logger.info(f"Created workflow tool: {tool_name}")
            return function_tool

        except Exception as e:
            logger.error(f"Failed to create workflow tool: {e}", exc_info=True)
            return None

    def create_workflow_tool_from_metadata(self, workflow_metadata):
        """
        Create a workflow tool from metadata.

        Args:
            workflow_metadata: Workflow metadata dictionary

        Returns:
            Tool: A configured Tool instance for the workflow
        """
        return self.workflow_tool_loader.create_workflow_tool_from_metadata(
            workflow_metadata
        )

    async def load_workflows_as_tools(self, workflows_endpoint="/api/v1/workflows"):
        """
        Load all available workflows as tools.

        Args:
            workflows_endpoint: API endpoint to get workflow metadata

        Returns:
            List of Tool instances for each workflow
        """
        return await self.workflow_tool_loader.load_workflows_as_tools(
            workflows_endpoint
        )
