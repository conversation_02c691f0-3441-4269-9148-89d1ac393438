from typing import Any, Dict, <PERSON>ple

from autogen_agentchat.base import TaskR<PERSON>ult
from autogen_agentchat.messages import (
    BaseChatMessage,
    ModelClientStreamingChunkEvent,
    MultiModalMessage,
    TextMessage,
    ToolCallExecutionEvent,
    ToolCallRequestEvent,
    ToolCallSummaryMessage,
    UserInputRequestedEvent,
)

from ..schemas.chat import MessageType


class MessageProcessor:
    """Handles message processing and extraction logic"""

    @staticmethod
    def get_message_type(message: Any) -> MessageType:
        """Determine message type from message instance"""
        type_mapping = {
            TextMessage: MessageType.TEXT,
            MultiModalMessage: MessageType.MULTIMODAL,
            ToolCallRequestEvent: MessageType.TOOL_CALL_REQUEST,
            ToolCallExecutionEvent: MessageType.TOOL_CALL_EXECUTION,
            ToolCallSummaryMessage: MessageType.TOOL_CALL_SUMMARY,
            TaskResult: MessageType.TASK_RESULT,
            ModelClientStreamingChunkEvent: MessageType.STREAMING_CHUNK,
            UserInputRequestedEvent: MessageType.USER_INPUT_REQUEST,
        }

        for msg_class, msg_type in type_mapping.items():
            if isinstance(message, msg_class):
                return msg_type

        return MessageType.UNKNOWN

    @staticmethod
    def extract_message_content(message: Any) -> Tuple[str, str]:
        """Extract source and content from message"""
        if isinstance(message, TextMessage):
            return getattr(message, "source", "assistant"), message.content
        elif isinstance(message, MultiModalMessage):
            # For multimodal messages, extract text content and summarize attachments
            source = getattr(message, "source", "assistant")
            content_parts = []

            for content_item in message.content:
                if isinstance(content_item, str):
                    content_parts.append(content_item)
                else:
                    # For non-text content (images, etc.), add a placeholder
                    content_parts.append(f"[Attachment: {type(content_item).__name__}]")

            content = (
                " ".join(content_parts) if content_parts else "[MultiModal Message]"
            )
            return source, content
        elif isinstance(
            message,
            (ToolCallRequestEvent, ToolCallExecutionEvent, ToolCallSummaryMessage),
        ):
            return "assistant", message.content
        elif isinstance(message, ModelClientStreamingChunkEvent):
            return getattr(message, "source", "assistant"), message.content
        elif isinstance(message, BaseChatMessage):
            return getattr(message, "source", "assistant"), message.content
        elif isinstance(message, UserInputRequestedEvent):
            return getattr(message, "source", "assistant"), message.content
        elif isinstance(message, TaskResult):
            return "assistant", ""
        else:
            return "assistant", getattr(message, "content", str(message))

    @staticmethod
    def extract_models_usage(message: Any) -> Dict[str, int]:
        """Extract token usage from message"""
        if hasattr(message, "models_usage") and message.models_usage:
            return {
                "prompt_tokens": getattr(message.models_usage, "prompt_tokens", 0),
                "completion_tokens": getattr(
                    message.models_usage, "completion_tokens", 0
                ),
            }
        return {"prompt_tokens": 0, "completion_tokens": 0}
