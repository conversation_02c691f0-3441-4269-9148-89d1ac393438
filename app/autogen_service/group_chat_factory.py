"""
Group Chat Factory for creating different types of AutoGen team configurations.
"""

import logging
from typing import Any, Dict, List, Optional, Union

from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.conditions import (
    ExternalTermination,
    MaxMessageTermination,
    TextMentionTermination,
)
from autogen_agentchat.teams import RoundRobinGroupChat, SelectorGroupChat
from autogen_ext.models.openai import OpenAIChatCompletionClient

from ..services.agent_group_service import AgentGroupService
from .model_factory import ModelFactory

logger = logging.getLogger(__name__)


class GroupChatFactory:
    """Factory for creating different types of group chat teams."""

    def __init__(self):
        self.logger = logger
        self.agent_group_service = AgentGroupService()

    async def create_group_chat_team(
        self,
        group_id: str,
        agents: List[AssistantAgent],
        chat_config: Optional[Dict[str, Any]] = None,
        model_client: Optional[OpenAIChatCompletionClient] = None,
    ) -> Optional[Union[RoundRobinGroupChat, SelectorGroupChat]]:
        """
        Create a group chat team based on group configuration.

        Args:
            group_id: The ID of the agent group
            agents: List of AssistantAgent instances
            chat_config: Optional chat configuration override
            model_client: Model client for selector group chat

        Returns:
            Group chat team instance or None if creation fails
        """
        try:
            if not agents:
                self.logger.error("Cannot create group chat with no agents")
                return None

            # Get group chat configuration
            if not chat_config:
                chat_config = await self.agent_group_service.get_group_chat_config(
                    group_id
                )

            if not chat_config:
                self.logger.warning(
                    f"No chat config found for group {group_id}, using defaults"
                )
                chat_config = self._get_default_chat_config()

            # Create termination conditions
            termination_condition = self._create_termination_conditions(chat_config)

            # Determine team type
            team_type = chat_config.get("team_type", "round_robin").lower()

            if team_type == "round_robin":
                return self._create_round_robin_team(
                    agents, termination_condition, chat_config
                )
            elif team_type == "selector":
                return await self._create_selector_team(
                    agents, termination_condition, chat_config, model_client
                )
            else:
                self.logger.warning(
                    f"Unknown team type: {team_type}, defaulting to round_robin"
                )
                return self._create_round_robin_team(
                    agents, termination_condition, chat_config
                )

        except Exception as e:
            self.logger.error(
                f"Error creating group chat team for group {group_id}: {e}"
            )
            return None

    def _create_round_robin_team(
        self,
        agents: List[AssistantAgent],
        termination_condition,
        chat_config: Dict[str, Any],
    ) -> RoundRobinGroupChat:
        """Create a RoundRobinGroupChat team."""
        try:
            self.logger.info(f"Creating RoundRobinGroupChat with {len(agents)} agents")

            team = RoundRobinGroupChat(
                participants=agents, termination_condition=termination_condition
            )

            self.logger.info("Successfully created RoundRobinGroupChat team")
            return team

        except Exception as e:
            self.logger.error(f"Error creating RoundRobinGroupChat: {e}")
            raise

    async def _create_selector_team(
        self,
        agents: List[AssistantAgent],
        termination_condition,
        chat_config: Dict[str, Any],
        model_client: Optional[OpenAIChatCompletionClient] = None,
    ) -> SelectorGroupChat:
        """Create a SelectorGroupChat team."""
        try:
            self.logger.info(f"Creating SelectorGroupChat with {len(agents)} agents")

            if not model_client:
                # Create a default model client for selector using ModelFactory
                model_config = {
                    "provider": "OpenAIChatCompletionClient",
                    "model": chat_config.get("selector_model", "gpt-4o-mini"),
                    "llm_type": "openai",
                }
                model_client = ModelFactory.create_model_client(model_config)

            team = SelectorGroupChat(
                participants=agents,
                model_client=model_client,
                termination_condition=termination_condition,
            )

            self.logger.info("Successfully created SelectorGroupChat team")
            return team

        except Exception as e:
            self.logger.error(f"Error creating SelectorGroupChat: {e}")
            raise

    def _create_termination_conditions(self, chat_config: Dict[str, Any]):
        """Create termination conditions based on configuration."""
        try:
            conditions = []

            # Max messages termination
            max_messages = chat_config.get("max_messages", 10)
            conditions.append(MaxMessageTermination(max_messages))

            # Text mention termination
            termination_keywords = chat_config.get(
                "termination_keywords", ["TERMINATE"]
            )
            for keyword in termination_keywords:
                conditions.append(TextMentionTermination(keyword))

            # External termination (for manual stopping)
            external_termination = ExternalTermination()
            conditions.append(external_termination)

            # Combine conditions with OR logic
            combined_condition = conditions[0]
            for condition in conditions[1:]:
                combined_condition = combined_condition | condition

            self.logger.info(
                f"Created termination conditions: max_messages={max_messages}, keywords={termination_keywords}"
            )
            return combined_condition

        except Exception as e:
            self.logger.error(f"Error creating termination conditions: {e}")
            # Return a simple max message termination as fallback
            return MaxMessageTermination(10)

    def _get_default_chat_config(self) -> Dict[str, Any]:
        """Get default chat configuration."""
        return {
            "team_type": "round_robin",
            "max_messages": 10,
            "termination_keywords": ["TERMINATE", "STOP", "END"],
            "selector_model": "gpt-4o-mini",
            "group_description": "Default group chat",
            "group_name": "Default Group",
        }

    async def create_single_agent_team(
        self, agent: AssistantAgent, max_messages: int = 5
    ) -> RoundRobinGroupChat:
        """
        Create a single-agent team for testing or simple scenarios.

        Args:
            agent: Single AssistantAgent instance
            max_messages: Maximum number of messages before termination

        Returns:
            RoundRobinGroupChat with single agent
        """
        try:
            self.logger.info(f"Creating single-agent team with agent: {agent.name}")

            termination_condition = MaxMessageTermination(
                max_messages
            ) | TextMentionTermination("TERMINATE")

            team = RoundRobinGroupChat(
                participants=[agent], termination_condition=termination_condition
            )

            self.logger.info("Successfully created single-agent team")
            return team

        except Exception as e:
            self.logger.error(f"Error creating single-agent team: {e}")
            raise

    def get_team_info(
        self, team: Union[RoundRobinGroupChat, SelectorGroupChat]
    ) -> Dict[str, Any]:
        """
        Get information about a team.

        Args:
            team: The team instance

        Returns:
            Dictionary with team information
        """
        try:
            team_info = {
                "team_type": type(team).__name__,
                "participant_count": (
                    len(team.participants) if hasattr(team, "participants") else 0
                ),
                "participant_names": (
                    [agent.name for agent in team.participants]
                    if hasattr(team, "participants")
                    else []
                ),
                "has_termination_condition": hasattr(team, "termination_condition")
                and team.termination_condition is not None,
            }

            return team_info

        except Exception as e:
            self.logger.error(f"Error getting team info: {e}")
            return {"error": str(e)}
