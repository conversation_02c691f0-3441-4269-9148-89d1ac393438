"""
GeneralKnowledgeAgent - A specialized agent for general knowledge queries using trained knowledge.

This module provides a GeneralKnowledgeAgent class that handles general knowledge
questions using the model's trained knowledge. It follows the established agent
patterns in the codebase while focusing specifically on providing comprehensive
answers from its training data and clearly indicating knowledge limitations.
"""

from __future__ import annotations

import logging
from typing import Optional

from autogen_agentchat.agents import AssistantAgent
from autogen_core.models import ChatCompletionClient

from ....shared.config.base import Settings, get_settings
from ...model_factory import ModelFactory

logger = logging.getLogger(__name__)


class GeneralKnowledgeAgent:
    """
    A specialized general knowledge agent that uses trained model knowledge for comprehensive responses.

    This agent is designed to:
    - Provide comprehensive answers using trained knowledge
    - Clearly indicate knowledge limitations and cutoff dates
    - Recommend web search for current/recent information it doesn't have
    - Deliver well-structured, accurate responses with proper context
    - Be transparent about what it knows vs. what requires current data
    """

    def __init__(self):
        """Initialize the GeneralKnowledgeAgent with default configuration."""
        self._settings: Settings = get_settings()
        self._model_client: Optional[ChatCompletionClient] = None
        self._agent: Optional[AssistantAgent] = None
        self._is_initialized: bool = False

    async def initialize(self) -> bool:
        """
        Initialize the general knowledge agent with model client.

        Returns:
            bool: True if initialization successful, False otherwise
        """
        try:
            # Create model client using ModelFactory
            model_config = {
                "provider": "OpenAIChatCompletionClient",
                "llm_type": "openai",
                "model": "gpt-4.1",
                "api_key": self._settings.requesty.api_key,
                "base_url": self._settings.requesty.base_url,
                "model_info": {
                    "vision": False,
                    "function_calling": True,
                    "json_output": True,
                    "structured_output": True,
                },
            }

            self._model_client = ModelFactory.create_model_client(model_config)
            if not self._model_client:
                logger.error("Failed to create model client for GeneralKnowledgeAgent")
                return False

            # Create the assistant agent (no external tools needed)
            self._agent = AssistantAgent(
                name="general_knowledge_agent",
                description="Expert general knowledge specialist providing comprehensive answers using trained knowledge with clear limitations awareness.",
                model_client=self._model_client,
                tools=[],  # No external tools - relies on model knowledge only
                reflect_on_tool_use=False,  # No tools to reflect on
                system_message=self._get_enhanced_system_message(),
                model_client_stream=True,
            )

            self._is_initialized = True
            logger.info("GeneralKnowledgeAgent initialized successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to initialize GeneralKnowledgeAgent: {str(e)}")
            self._is_initialized = False
            return False

    def _get_enhanced_system_message(self) -> str:
        """
        Get the enhanced system message for the general knowledge agent.

        Returns:
            str: Comprehensive system message for general knowledge operations
        """
        return """
            You are a specialized General Knowledge Expert with extensive training across diverse domains.

            CORE RESPONSIBILITIES:
            - Provide comprehensive, accurate answers using your trained knowledge
            - Clearly indicate when information is beyond your knowledge cutoff date
            - Recommend web search for current events, recent developments, or real-time data
            - Deliver well-structured, contextual responses with proper explanations
            - Be transparent about knowledge limitations and uncertainty levels

            KNOWLEDGE APPROACH:
            1. **Comprehensive Coverage**: Draw from your extensive training across multiple domains
            2. **Contextual Depth**: Provide background context and related information
            3. **Accuracy Priority**: Focus on providing verified, well-established information
            4. **Clear Explanations**: Break down complex topics into understandable components
            5. **Cross-Domain Connections**: Link related concepts across different fields when relevant

            KNOWLEDGE LIMITATIONS AWARENESS:
            - Your knowledge has a training cutoff date - clearly state this when relevant
            - For questions about current events, recent developments, or real-time information:
              * Acknowledge the limitation explicitly
              * Provide what historical context you can
              * Recommend using web search for current information
            - For rapidly changing fields (technology, politics, markets), note when information might be outdated

            RESPONSE STRUCTURE:
            - Start with a direct, clear answer to the main question
            - Provide detailed supporting information organized logically
            - Include relevant background, context, and related concepts
            - Use examples, analogies, or illustrations when helpful
            - Clearly separate facts from interpretations or theories
            - End with key takeaways or summary points

            TRANSPARENCY REQUIREMENTS:
            - Explicitly state when information might be outdated due to knowledge cutoff
            - Distinguish between well-established facts and areas of ongoing debate
            - Acknowledge uncertainty when information is incomplete or conflicting
            - Recommend web search when current/recent information is specifically needed
            - Note when questions require real-time data, current statistics, or recent developments

            QUALITY STANDARDS:
            - Prioritize accuracy and reliability over speculation
            - Provide multiple perspectives on controversial or debated topics
            - Include relevant caveats and nuances in complex subjects
            - Cross-reference information across domains when applicable
            - Maintain academic rigor while ensuring accessibility

            CURRENT FOCUS:
            When answering questions, leverage your comprehensive training to provide thorough, well-contextualized responses. Always be transparent about knowledge limitations and proactively recommend web search for information that requires current data or falls outside your training cutoff.

            KNOWLEDGE CUTOFF GUIDANCE:
            Your training data has a specific cutoff date. When users ask about:
            - Recent events, news, or developments
            - Current statistics, prices, or real-time data
            - Recently released products, services, or content
            - Ongoing situations or evolving circumstances
            
            You should explicitly mention your knowledge cutoff and recommend using web search tools for the most current information while providing whatever historical context and background you can offer.
            """

    def get_agent(self) -> Optional[AssistantAgent]:
        """
        Get the underlying AssistantAgent instance.

        Returns:
            Optional[AssistantAgent]: The agent instance if initialized, None otherwise
        """
        return self._agent if self._is_initialized else None

    def is_initialized(self) -> bool:
        """
        Check if the agent is properly initialized.

        Returns:
            bool: True if initialized, False otherwise
        """
        return self._is_initialized

    @classmethod
    async def create_and_initialize(cls) -> Optional["GeneralKnowledgeAgent"]:
        """
        Create and initialize a GeneralKnowledgeAgent instance.

        Returns:
            Optional[GeneralKnowledgeAgent]: Initialized agent instance or None if failed
        """
        agent = cls()
        if await agent.initialize():
            return agent
        else:
            logger.error("Failed to create and initialize GeneralKnowledgeAgent")
            return None
