import logging
from typing import Optional

from autogen_agentchat.agents import Assistant<PERSON>gent
from autogen_core.models import ChatCompletionClient

from app.shared.config.base import Settings, get_settings

from ...model_factory import ModelFactory

logger = logging.getLogger(__name__)


class QueryAnalysisAgent:
    """
    Agent responsible for analyzing user queries and extracting task requirements
    in a structured format for the discovery pipeline.
    """

    def __init__(self):
        """Initialize the QueryAnalysisAgent with default configuration."""
        self._settings: Settings = get_settings()
        self._model_client: Optional[ChatCompletionClient] = None
        self._agent: Optional[AssistantAgent] = None
        self._is_initialized: bool = False

    async def initialize(self) -> None:
        """Initialize the agent with model client and context."""
        try:
            logger.info("Initializing Query Analysis Agent...")

            # Create model client using ModelFactory
            model_config = {
                "provider": "OpenAIChatCompletionClient",
                "llm_type": "openai",
                "model": "gpt-4.1",
                "api_key": self._settings.requesty.api_key,
                "base_url": self._settings.requesty.base_url,
                "model_info": {
                    "vision": False,
                    "function_calling": True,
                    "json_output": True,
                    "structured_output": True,
                },
            }

            self._model_client = ModelFactory.create_model_client(model_config)
            if not self._model_client:
                logger.error("Failed to create model client for Selection Agent")
                return False

            # Create the assistant agent
            self._agent = AssistantAgent(
                name="query_analysis_agent",
                description="Analyzes user queries to extract requirements and categorize tasks",
                model_client=self._model_client,
                system_message=self._get_enhanced_system_message(),
                tools=[],
                reflect_on_tool_use=False,
            )

            self._is_initialized = True
            logger.info("QueryAnalysisAgent initialized successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to initialize QueryAnalysisAgent : {str(e)}")
            self._is_initialized = False
            return False

    def _get_enhanced_system_message(self) -> str:
        return """You are a Query Analysis Agent specialized in analyzing user queries to extract key information for agent routing.

                Your task is to analyze user queries and extract:
                1. Domain/category of the query
                2. Task summary in one clear sentence
                3. Technical requirements needed
                4. Keywords for agent matching
                5. Urgency level (low, medium, high)
                6. Complexity level (simple, moderate, complex)

                Always respond in a structured format with clear categorization. Focus on understanding the user's intent and technical needs."""

    def get_agent(self) -> Optional[AssistantAgent]:
        """
        Get the underlying AssistantAgent instance.

        Returns:
            Optional[AssistantAgent]: The agent instance if initialized, None otherwise
        """
        return self._agent if self._is_initialized else None

    def is_initialized(self) -> bool:
        """
        Check if the agent is properly initialized.

        Returns:
            bool: True if initialized, False otherwise
        """
        return self._is_initialized

    @classmethod
    async def create_and_initialize(cls) -> Optional["QueryAnalysisAgent"]:
        """
        Convenience method to create and initialize the Selector system in one call.

        Returns:
            QueryAnalysisAgent: Initialized QueryAnalysisAgent
        """
        agent = cls()
        if await agent.initialize():
            return agent
        else:
            logger.error("Failed to create and initialize QueryAnalysisAgent")
            return None
