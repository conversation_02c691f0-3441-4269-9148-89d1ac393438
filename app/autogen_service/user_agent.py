import asyncio
from typing import Dict
from autogen_core import (
    Message<PERSON><PERSON>x<PERSON>,
    RoutedAgent,
    message_handler,
)
from autogen_core.models import (
    UserMessage,
    AssistantMessage,
    SystemMessage,
)
from api.models import AgentResponse


class APIUserAgent(RoutedAgent):
    """
    Agent that handles user interactions via the API
    """

    def __init__(
        self,
        description: str,
        user_topic_type: str,
        agent_topic_type: str,
        response_queue: asyncio.Queue,
    ) -> None:
        super().__init__(description)
        self._user_topic_type = user_topic_type
        self._agent_topic_type = agent_topic_type
        self._response_queue = response_queue

    @message_handler
    async def handle_task_result(
        self, message: AgentResponse, ctx: MessageContext
    ) -> None:
        """
        Handle task results from other agents

        Parameters:
        -----------
        message: AgentResponse
            The agent response message
        ctx: MessageContext
            The message context
        """
        conversation_history = []
        final_response = ""

        for msg in message.context:
            if isinstance(msg, (<PERSON><PERSON><PERSON><PERSON><PERSON>, AssistantMessage, SystemMessage)):
                # Create a dictionary representation of the message
                entry = {
                    "role": msg.source,
                    "content": (
                        str(msg.content)
                        if not isinstance(msg.content, str)
                        else msg.content
                    ),
                }
                conversation_history.append(entry)

                # Update final response if it's not from the user
                if entry["role"] != "User":
                    final_response = entry["content"]

        await self._response_queue.put(
            {
                "conversation_history": conversation_history,
                "final_response": final_response,
            }
        )
