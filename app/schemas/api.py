from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field


class ToolConfig(BaseModel):
    name: str
    type: str
    code: Optional[str] = None  # For dynamic tools


class AgentConfig(BaseModel):
    """Configuration for an agent."""

    id: str
    name: str
    description: Optional[str] = None
    avatar: Optional[str] = None
    owner_id: Optional[str] = None
    user_ids: Optional[List[str]] = Field(default_factory=list)
    owner_type: Optional[str] = None
    template_id: Optional[str] = None
    template_owner_id: Optional[str] = None
    parent_agent_id: Optional[str] = None
    url: Optional[str] = None
    is_imported: Optional[bool] = False
    is_bench_employee: Optional[bool] = False
    is_changes_marketplace: Optional[bool] = False
    agent_category: Optional[str] = None
    system_message: Optional[str] = None
    model_provider: Optional[str] = None
    model_name: Optional[str] = None
    model_api_key: Optional[str] = None
    workflow_ids: Optional[List[str]] = Field(default_factory=list)
    mcp_server_ids: Optional[List[str]] = Field(default_factory=list)
    agent_topic_type: Optional[str] = None
    subscriptions: Optional[Any] = None
    visibility: Optional[str] = None
    category: Optional[str] = None
    tags: Optional[Dict[str, Any]] = Field(
        default_factory=dict, description="JSON tags for the agent"
    )
    status: Optional[str] = None
    department: Optional[str] = None
    organization_id: Optional[str] = None
    tone: Optional[str] = None
    files: Optional[List[str]] = Field(default_factory=list)
    urls: Optional[List[str]] = Field(default_factory=list)
    ruh_credentials: Optional[bool] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    agent_tools: Optional[List[Dict[str, Any]]] = Field(default_factory=list)
    mcps: Optional[List[Dict[str, Any]]] = Field(default_factory=list)
    workflows: Optional[List[Dict[str, Any]]] = Field(default_factory=list)


class TeamConfig(BaseModel):
    """Configuration for a team of agents."""

    team_type: str = Field(
        default="round_robin",
        description="Type of team to create (round_robin, selector, sequential, or custom class path)",
    )
    team_kwargs: Optional[Dict[str, Any]] = Field(
        default=None, description="Additional arguments for the team constructor"
    )
    termination_configs: Optional[List[Dict[str, Any]]] = Field(
        default=None, description="Configuration for termination conditions"
    )


class Session(BaseModel):
    session_id: str
    agent_id: str
    agent_config: Optional[AgentConfig] = None
