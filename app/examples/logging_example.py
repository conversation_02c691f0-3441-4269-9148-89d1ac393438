"""
Example demonstrating the enhanced logging system with multiple log levels.

This example shows how to:
1. Configure logging with multiple log levels
2. Use component-specific loggers
3. Log to specific log levels
4. Log to multiple log levels simultaneously
5. Log with additional context

Usage:
    # Log all messages (DEBUG, INFO, WARNING, ERROR, CRITICAL)
    LOG_LEVEL=DEBUG,INFO,WARNING,ERROR,CRITICAL python -m app.examples.logging_example

    # Log only specific levels
    LOG_LEVEL=INFO,ERROR python -m app.examples.logging_example

    # Set different log levels for different components
    LOG_LEVEL=INFO KAFKA_LOG_LEVEL=DEBUG SESSION_LOG_LEVEL=WARNING python -m app.examples.logging_example

    # Use JSON format
    LOG_FORMAT=json LOG_LEVEL=INFO,DEBUG python -m app.examples.logging_example
"""

import os
import logging
from ..shared.config.logging_config import (
    setup_logging,
    get_logger,
    get_level_logger,
    log_with_context,
    log_to_all_levels,
)

# Set up logging
use_json = os.getenv("LOG_FORMAT", "").lower() == "json"
setup_logging(
    default_level=os.getenv("LOG_LEVEL", "INFO"),
    logs_dir=os.getenv("LOGS_DIR", "logs"),
    use_json=use_json,
)

# Get regular logger
logger = get_logger(__name__)

# Get component-specific loggers
kafka_logger = get_logger("app.kafka_client.example")
session_logger = get_logger("app.helper.session_manager.example")
redis_logger = get_logger("app.helper.redis_client.example")

# Get level-specific loggers
debug_logger = get_level_logger("debug")
info_logger = get_level_logger("info")
warning_logger = get_level_logger("warning")
error_logger = get_level_logger("error")
critical_logger = get_level_logger("critical")


def demonstrate_basic_logging():
    """Demonstrate basic logging at different levels."""
    print("\n=== Basic Logging ===")

    logger.debug("This is a DEBUG message")
    logger.info("This is an INFO message")
    logger.warning("This is a WARNING message")
    logger.error("This is an ERROR message")
    logger.critical("This is a CRITICAL message")


def demonstrate_component_logging():
    """Demonstrate component-specific logging."""
    print("\n=== Component-Specific Logging ===")

    kafka_logger.info("Kafka component INFO message")
    kafka_logger.debug("Kafka component DEBUG message")

    session_logger.info("Session manager INFO message")
    session_logger.warning("Session manager WARNING message")

    redis_logger.info("Redis client INFO message")
    redis_logger.error("Redis client ERROR message")


def demonstrate_level_specific_logging():
    """Demonstrate level-specific logging."""
    print("\n=== Level-Specific Logging ===")

    debug_logger.debug("This will ONLY appear in DEBUG logs")
    info_logger.info("This will ONLY appear in INFO logs")
    warning_logger.warning("This will ONLY appear in WARNING logs")
    error_logger.error("This will ONLY appear in ERROR logs")
    critical_logger.critical("This will ONLY appear in CRITICAL logs")


def demonstrate_context_logging():
    """Demonstrate logging with additional context."""
    print("\n=== Context Logging ===")

    # Log with context
    log_with_context(
        logger,
        logging.INFO,
        "Message with context",
        extra={
            "user_id": "user123",
            "session_id": "session456",
            "request_id": "req789",
        },
    )

    # Log to multiple levels
    log_with_context(
        logger,
        logging.WARNING,
        "Message to multiple levels",
        extra={"operation": "multi-level-log"},
        log_to_levels=["info", "warning", "error"],
    )

    # Log to all levels
    log_to_all_levels(
        "This message appears at ALL log levels", extra={"source": "all-levels-example"}
    )


def main():
    """Run the logging examples."""
    print("Running logging examples...")
    print(f"Current LOG_LEVEL: {os.getenv('LOG_LEVEL', 'INFO')}")
    print(f"JSON format: {use_json}")

    demonstrate_basic_logging()
    demonstrate_component_logging()
    demonstrate_level_specific_logging()
    demonstrate_context_logging()

    print("\nCheck the log files in the 'logs' directory for complete output.")


if __name__ == "__main__":
    main()
