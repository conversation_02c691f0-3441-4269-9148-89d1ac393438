"""
Pinecone Memory Manager for managing agent memories and knowledge.

This module provides high-level management functions for Pinecone-based
agent memories, including knowledge indexing and retrieval.
"""

import logging
from typing import List, Dict, Any, Optional

from datetime import datetime

from autogen_core.memory import MemoryContent, MemoryMimeType
from .pinecone_memory import PineconeMemory
from ..shared.config.base import get_settings


logger = logging.getLogger(__name__)


class PineconeMemoryManager:
    """
    High-level manager for Pinecone-based agent memories.

    Provides utilities for managing agent knowledge, memories, and
    context across different agents and users.
    """

    def __init__(self):
        self.settings = get_settings()
        self._memory_instances: Dict[str, PineconeMemory] = {}

    def get_agent_memory(
        self,
        agent_id: str,
        user_id: Optional[str] = None,
        namespace: Optional[str] = None,
        **kwargs,
    ) -> PineconeMemory:
        """
        Get or create a Pinecone memory instance for an agent.

        Args:
            agent_id: Unique identifier for the agent
            user_id: Optional user identifier
            namespace: Optional namespace override
            **kwargs: Additional arguments for PineconeMemory

        Returns:
            PineconeMemory instance for the agent
        """
        # Create a unique key for this memory instance
        memory_key = f"{agent_id}:{user_id or 'global'}:{namespace or 'default'}"

        if memory_key not in self._memory_instances:
            self._memory_instances[memory_key] = PineconeMemory(
                agent_id=agent_id, user_id=user_id, namespace=namespace, **kwargs
            )

        return self._memory_instances[memory_key]

    async def add_agent_knowledge(
        self,
        agent_id: str,
        knowledge_items: List[Dict[str, Any]],
        user_id: Optional[str] = None,
        knowledge_type: str = "general",
    ) -> int:
        """
        Add knowledge items to an agent's memory.

        Args:
            agent_id: Agent identifier
            knowledge_items: List of knowledge items with 'content' and optional 'metadata'
            user_id: Optional user identifier
            knowledge_type: Type of knowledge (e.g., 'general', 'domain_specific', 'user_preferences')

        Returns:
            Number of knowledge items added
        """
        try:
            memory = self.get_agent_memory(agent_id, user_id)
            added_count = 0

            for item in knowledge_items:
                content = item.get("content", "")
                if not content:
                    continue

                # Prepare metadata
                metadata = item.get("metadata", {})
                metadata.update(
                    {
                        "knowledge_type": knowledge_type,
                        "added_at": datetime.utcnow().isoformat(),
                        "source": item.get("source", "manual"),
                    }
                )

                # Create memory content
                memory_content = MemoryContent(
                    content=content, mime_type=MemoryMimeType.TEXT, metadata=metadata
                )

                await memory.add(memory_content)
                added_count += 1

            logger.info(f"Added {added_count} knowledge items to agent {agent_id}")
            return added_count

        except Exception as e:
            logger.error(f"Error adding knowledge to agent {agent_id}: {e}")
            return 0

    async def add_conversation_memory(
        self,
        agent_id: str,
        user_message: str,
        agent_response: str,
        user_id: Optional[str] = None,
        conversation_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> bool:
        """
        Add a conversation exchange to agent memory.

        Args:
            agent_id: Agent identifier
            user_message: User's message
            agent_response: Agent's response
            user_id: Optional user identifier
            conversation_id: Optional conversation identifier
            metadata: Additional metadata

        Returns:
            True if successful, False otherwise
        """
        try:
            memory = self.get_agent_memory(agent_id, user_id)

            # Prepare conversation metadata
            conv_metadata = metadata or {}
            conv_metadata.update(
                {
                    "type": "conversation",
                    "conversation_id": conversation_id or "unknown",
                    "timestamp": datetime.utcnow().isoformat(),
                    "user_id": user_id or "unknown",
                }
            )

            # Create conversation context
            conversation_content = f"User: {user_message}\nAgent: {agent_response}"

            memory_content = MemoryContent(
                content=conversation_content,
                mime_type=MemoryMimeType.TEXT,
                metadata=conv_metadata,
            )

            await memory.add(memory_content)
            logger.debug(f"Added conversation memory for agent {agent_id}")
            return True

        except Exception as e:
            logger.error(f"Error adding conversation memory for agent {agent_id}: {e}")
            return False

    async def query_agent_memory(
        self,
        agent_id: str,
        query: str,
        user_id: Optional[str] = None,
        memory_types: Optional[List[str]] = None,
        k: int = 5,
    ) -> List[MemoryContent]:
        """
        Query an agent's memory for relevant information.

        Args:
            agent_id: Agent identifier
            query: Query string
            user_id: Optional user identifier
            memory_types: Optional list of memory types to filter by
            k: Number of results to return

        Returns:
            List of relevant memory contents
        """
        try:
            memory = self.get_agent_memory(agent_id, user_id)
            results = await memory.query(query, k=k)

            # Filter by memory types if specified
            if memory_types:
                filtered_results = []
                for result in results:
                    result_type = result.metadata.get("type") or result.metadata.get(
                        "knowledge_type"
                    )
                    if result_type in memory_types:
                        filtered_results.append(result)
                results = filtered_results

            logger.debug(f"Retrieved {len(results)} memories for agent {agent_id}")
            return results

        except Exception as e:
            logger.error(f"Error querying memory for agent {agent_id}: {e}")
            return []

    async def get_agent_memory_stats(
        self,
        agent_id: str,
        user_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Get memory statistics for an agent.

        Args:
            agent_id: Agent identifier
            user_id: Optional user identifier

        Returns:
            Dictionary with memory statistics
        """
        try:
            memory = self.get_agent_memory(agent_id, user_id)
            stats = await memory.get_stats()
            return stats
        except Exception as e:
            logger.error(f"Error getting memory stats for agent {agent_id}: {e}")
            return {}

    async def clear_agent_memory(
        self,
        agent_id: str,
        user_id: Optional[str] = None,
        memory_type: Optional[str] = None,
    ) -> bool:
        """
        Clear an agent's memory.

        Args:
            agent_id: Agent identifier
            user_id: Optional user identifier
            memory_type: Optional memory type to clear (if None, clears all)

        Returns:
            True if successful, False otherwise
        """
        try:
            memory = self.get_agent_memory(agent_id, user_id)

            if memory_type:
                # TODO: Implement selective clearing by memory type
                # This would require querying and deleting specific vectors
                logger.warning("Selective memory clearing by type not yet implemented")
                return False
            else:
                await memory.clear()
                logger.info(f"Cleared all memory for agent {agent_id}")
                return True

        except Exception as e:
            logger.error(f"Error clearing memory for agent {agent_id}: {e}")
            return False

    async def transfer_knowledge_between_agents(
        self,
        source_agent_id: str,
        target_agent_id: str,
        query: str,
        user_id: Optional[str] = None,
        k: int = 5,
    ) -> int:
        """
        Transfer knowledge from one agent to another based on a query.

        Args:
            source_agent_id: Source agent identifier
            target_agent_id: Target agent identifier
            query: Query to find relevant knowledge
            user_id: Optional user identifier
            k: Number of knowledge items to transfer

        Returns:
            Number of knowledge items transferred
        """
        try:
            # Query source agent's memory
            source_memories = await self.query_agent_memory(
                source_agent_id, query, user_id, k=k
            )

            if not source_memories:
                return 0

            # Add to target agent's memory
            target_memory = self.get_agent_memory(target_agent_id, user_id)
            transferred_count = 0

            for memory in source_memories:
                # Update metadata to indicate transfer
                new_metadata = memory.metadata.copy()
                new_metadata.update(
                    {
                        "transferred_from": source_agent_id,
                        "transferred_at": datetime.utcnow().isoformat(),
                        "original_score": new_metadata.get("score", 0),
                    }
                )

                new_memory = MemoryContent(
                    content=memory.content,
                    mime_type=memory.mime_type,
                    metadata=new_metadata,
                )

                await target_memory.add(new_memory)
                transferred_count += 1

            logger.info(
                f"Transferred {transferred_count} knowledge items from {source_agent_id} to {target_agent_id}"
            )
            return transferred_count

        except Exception as e:
            logger.error(f"Error transferring knowledge between agents: {e}")
            return 0

    async def close_all_memories(self):
        """Close all memory instances."""
        for memory in self._memory_instances.values():
            await memory.close()
        self._memory_instances.clear()
        logger.info("Closed all memory instances")


# Global memory manager instance
memory_manager = PineconeMemoryManager()
