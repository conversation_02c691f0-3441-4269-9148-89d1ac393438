import asyncio
import logging
import sys
import time
from datetime import datetime
from pathlib import Path

# Add the app directory to the Python path
sys.path.append(str(Path(__file__).parent.parent.parent / "app"))

from autogen_service.discovery_agents.discovery_master_agent_group_chat import (
    DiscoveryMasterAgentGroupChat,
)

# Set up detailed logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)

logger = logging.getLogger(__name__)


class DetailedLogger:
    """Helper class for detailed step-by-step logging with emojis"""

    @staticmethod
    def step_header(step_num: int, title: str, emoji: str = "🔄"):
        print(f"\n{emoji} Step {step_num}: {title}")
        print("=" * 60)

    @staticmethod
    def agent_start(agent_name: str, emoji: str = "🤖"):
        print(f"\n{emoji} {agent_name} is starting...")
        print("-" * 40)

    @staticmethod
    def agent_input(agent_name: str, input_data: str, emoji: str = "📥"):
        print(f"{emoji} {agent_name} Input:")
        print(f"   {input_data}")

    @staticmethod
    def agent_output(agent_name: str, output_data: str, emoji: str = "📤"):
        print(f"{emoji} {agent_name} Output:")
        print(f"   {output_data}")

    @staticmethod
    def agent_complete(agent_name: str, emoji: str = "✅"):
        print(f"{emoji} {agent_name} completed")
        print("-" * 40)

    @staticmethod
    def info(message: str, emoji: str = "ℹ️"):
        print(f"{emoji} {message}")

    @staticmethod
    def error(message: str, emoji: str = "❌"):
        print(f"{emoji} Error: {message}")

    @staticmethod
    def success(message: str, emoji: str = "✅"):
        print(f"{emoji} {message}")

    @staticmethod
    def timing(phase: str, duration: float, emoji: str = "⏱️"):
        print(f"{emoji} {phase}: {duration:.3f}s")

    @staticmethod
    def timing_summary(phase: str, duration: float, emoji: str = "📊"):
        print(f"{emoji} {phase} completed in {duration:.3f}s")


class TimingTracker:
    """Helper class to track timing for different phases"""

    def __init__(self):
        self.phase_times = {}
        self.step_times = {}
        self.start_time = None

    def start_total(self):
        """Start tracking total time"""
        self.start_time = time.time()

    def start_phase(self, phase_name: str):
        """Start tracking a phase"""
        self.phase_times[phase_name] = {"start": time.time()}

    def end_phase(self, phase_name: str):
        """End tracking a phase"""
        if phase_name in self.phase_times:
            self.phase_times[phase_name]["end"] = time.time()
            self.phase_times[phase_name]["duration"] = (
                self.phase_times[phase_name]["end"]
                - self.phase_times[phase_name]["start"]
            )

    def start_step(self, step_name: str):
        """Start tracking a step"""
        self.step_times[step_name] = {"start": time.time()}

    def end_step(self, step_name: str):
        """End tracking a step"""
        if step_name in self.step_times:
            self.step_times[step_name]["end"] = time.time()
            self.step_times[step_name]["duration"] = (
                self.step_times[step_name]["end"] - self.step_times[step_name]["start"]
            )

    def get_total_time(self):
        """Get total elapsed time"""
        if self.start_time:
            return time.time() - self.start_time
        return 0

    def get_phase_duration(self, phase_name: str):
        """Get duration of a specific phase"""
        return self.phase_times.get(phase_name, {}).get("duration", 0)

    def get_step_duration(self, step_name: str):
        """Get duration of a specific step"""
        return self.step_times.get(step_name, {}).get("duration", 0)


async def test_discovery_agent_workflow(query: str):
    """
    Complete end-to-end test of the discovery agent workflow with detailed logging.

    Args:
        query: User query to process

    Returns:
        Final response from the assigned specialized agent or "agent not found"
    """

    print("🔍 Discovery Agent Workflow Test")
    print("=" * 80)
    print(f"Query: {query}")
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)

    # Initialize timing tracker
    timer = TimingTracker()
    timer.start_total()

    try:
        # Initialize the discovery system
        DetailedLogger.step_header(1, "Initializing Discovery System", "🏗️")
        timer.start_phase("initialization")

        discovery_system = await DiscoveryMasterAgentGroupChat.create_and_initialize(
            chat_type="round_robin"
        )

        timer.end_phase("initialization")
        init_time = timer.get_phase_duration("initialization")
        DetailedLogger.timing_summary(
            "Discovery system initialization", init_time, "✅"
        )

        # Run the complete workflow with detailed individual agent timing
        DetailedLogger.step_header(2, "Running 4-Agent Discovery Workflow", "🔄")
        timer.start_phase("workflow")

        # Instead of calling the existing method, we'll implement our own with detailed timing
        DetailedLogger.info(
            "Starting orchestrated workflow with individual agent timing..."
        )

        # Validate group chat is initialized
        if not discovery_system.group_chat_team:
            raise ValueError("Group chat not initialized. Call initialize() first.")

        print(f"\n🚀 Starting 4-Agent Discovery Workflow")
        print(f"📝 User Query: {query}")
        print(f"🤖 Agents: Query Analysis, Discovery, Selection, Assignment")
        print("-" * 60)

        workflow_results = []

        # Step 1: Query Analysis Agent
        timer.start_step("query_analysis_agent")
        print(f"\n🔍 STEP 1: Query Analysis Agent")
        print(f"📥 Input: User query to analyze")
        print(f"   '{query}'")
        print(f"🤖 Query Analysis Agent is processing...")

        analysis_results = await discovery_system.query_agent.analyze_query(query)

        timer.end_step("query_analysis_agent")
        query_analysis_time = timer.get_step_duration("query_analysis_agent")

        analysis_content = (
            f"TASK_SUMMARY: {analysis_results.get('task_summary', 'N/A')}\n"
        )
        analysis_content += f"DOMAIN: {analysis_results.get('domain', 'N/A')}\n"
        analysis_content += (
            f"REQUIREMENTS: {', '.join(analysis_results.get('requirements', []))}\n"
        )
        analysis_content += (
            f"KEYWORDS: {', '.join(analysis_results.get('keywords', []))}\n"
        )
        analysis_content += f"URGENCY: {analysis_results.get('urgency', 'medium')}\n"
        analysis_content += (
            f"COMPLEXITY: {analysis_results.get('complexity', 'moderate')}"
        )

        print(f"📤 Query Analysis Agent Output:")
        print(f"   Domain: {analysis_results.get('domain', 'N/A')}")
        print(f"   Task: {analysis_results.get('task_summary', 'N/A')}")
        print(f"   Requirements: {', '.join(analysis_results.get('requirements', []))}")
        print(f"   Keywords: {', '.join(analysis_results.get('keywords', []))}")
        print(f"✅ Query Analysis Agent completed in {query_analysis_time:.3f}s")

        workflow_results.append(
            {
                "agent": "query_analysis_agent",
                "content": analysis_content,
                "timestamp": asyncio.get_event_loop().time(),
                "duration": query_analysis_time,
            }
        )

        # Step 2: Discovery Agent
        timer.start_step("discovery_agent")
        print(f"\n🔎 STEP 2: Discovery Agent")
        print(f"📥 Input: Analysis results from Query Analysis Agent")
        print(f"   Domain: {analysis_results.get('domain', 'N/A')}")
        print(f"   Keywords: {', '.join(analysis_results.get('keywords', []))}")
        print(f"🤖 Discovery Agent is searching agent registry...")

        discovery_results = await discovery_system.discovery_agent.discover_agents(
            analysis_results
        )
        discovered_agents = discovery_results.get("discovered_agents", [])

        timer.end_step("discovery_agent")
        discovery_time = timer.get_step_duration("discovery_agent")

        print(f"📤 Discovery Agent Output:")
        print(f"   Found {len(discovered_agents)} suitable agents:")
        for i, agent in enumerate(discovered_agents, 1):
            print(
                f"   {i}. {agent.get('agent_name', 'Unknown')} (Score: {agent.get('score', 0.0)})"
            )
            print(f"      ID: {agent.get('agent_id', 'unknown')}")
            print(f"      Reason: {agent.get('reason', 'N/A')}")
        print(f"✅ Discovery Agent completed in {discovery_time:.3f}s")

        discovery_content = "DISCOVERY_RESULTS:\n"
        for i, agent in enumerate(discovered_agents, 1):
            discovery_content += f"AGENT_{i}: {agent.get('agent_id', 'unknown')} | SCORE: {agent.get('score', 0.0)} | REASON: {agent.get('reason', 'N/A')}\n"
        discovery_content += f"SUMMARY: Found {len(discovered_agents)} suitable agents"

        workflow_results.append(
            {
                "agent": "discovery_agent",
                "content": discovery_content,
                "timestamp": asyncio.get_event_loop().time(),
                "duration": discovery_time,
            }
        )

        # Step 3: Selection Agent
        timer.start_step("selection_agent")
        print(f"\n🎯 STEP 3: Selection Agent")
        print(
            f"📥 Input: Discovery results with {len(discovered_agents)} candidate agents"
        )
        for i, agent in enumerate(discovered_agents, 1):
            print(
                f"   Candidate {i}: {agent.get('agent_name', 'Unknown')} (Score: {agent.get('score', 0.0)})"
            )
        print(f"🤖 Selection Agent is evaluating candidates...")

        selection_results = await discovery_system.selection_agent.select_agent(
            discovery_results
        )
        selected_agent = selection_results.get("selected_agent")

        timer.end_step("selection_agent")
        selection_time = timer.get_step_duration("selection_agent")

        print(f"📤 Selection Agent Output:")
        if selected_agent:
            print(f"   Selected: {selected_agent.get('agent_name', 'Unknown')}")
            print(f"   Agent ID: {selected_agent.get('agent_id', 'N/A')}")
            print(f"   Confidence: {selection_results.get('confidence', 0.0)}")
            print(f"   Reason: {selection_results.get('reason', 'N/A')}")
        else:
            print(f"   No agent selected")
            print(
                f"   Reason: {selection_results.get('reason', 'No suitable agent found')}"
            )
        print(f"✅ Selection Agent completed in {selection_time:.3f}s")

        selection_content = f"SELECTED_AGENT: {selected_agent.get('agent_id', 'None') if selected_agent else 'None'}\n"
        selection_content += f"CONFIDENCE: {selection_results.get('confidence', 0.0)}\n"
        selection_content += f"PRIMARY_REASON: {selection_results.get('reason', 'N/A')}"

        workflow_results.append(
            {
                "agent": "selection_agent",
                "content": selection_content,
                "timestamp": asyncio.get_event_loop().time(),
                "duration": selection_time,
            }
        )

        # Step 4: Assignment Agent
        timer.start_step("assignment_agent")
        print(f"\n📋 STEP 4: Assignment Agent")
        if selected_agent:
            print(f"📥 Input: Selected agent for task assignment")
            print(f"   Agent: {selected_agent.get('agent_name', 'Unknown')}")
            print(f"   Task: {analysis_results.get('task_summary', 'N/A')}")
        else:
            print(f"📥 Input: No agent selected - will create 'no assignment' result")
        print(f"🤖 Assignment Agent is creating task assignment...")

        assignment_results = await discovery_system.assignment_agent.create_assignment(
            selection_results
        )
        assignment_details = assignment_results.get("assignment_details", {})

        timer.end_step("assignment_agent")
        assignment_time = timer.get_step_duration("assignment_agent")

        print(f"📤 Assignment Agent Output:")
        if assignment_results.get("assignment_created"):
            print(f"   Assigned To: {assignment_details.get('assigned_to', 'N/A')}")
            print(f"   Priority: {assignment_details.get('priority', 'medium')}")
            print(f"   Objective: {assignment_details.get('objective', 'N/A')}")
            print(
                f"   Deliverables: {', '.join(assignment_details.get('deliverables', []))}"
            )
        else:
            print(
                f"   Assignment failed: {assignment_results.get('reason', 'Unknown error')}"
            )
        print(f"✅ Assignment Agent completed in {assignment_time:.3f}s")

        assignment_content = f"TASK_ASSIGNMENT:\n"
        assignment_content += (
            f"ASSIGNED_TO: {assignment_details.get('assigned_to', 'N/A')}\n"
        )
        assignment_content += (
            f"PRIORITY: {assignment_details.get('priority', 'medium')}\n"
        )
        assignment_content += (
            f"OBJECTIVE: {assignment_details.get('objective', 'N/A')}\n"
        )
        assignment_content += (
            f"DELIVERABLES: {', '.join(assignment_details.get('deliverables', []))}\n"
        )
        assignment_content += "WORKFLOW_COMPLETE"

        workflow_results.append(
            {
                "agent": "assignment_agent",
                "content": assignment_content,
                "timestamp": asyncio.get_event_loop().time(),
                "duration": assignment_time,
            }
        )

        print(f"\n🎉 4-Agent Workflow Complete!")
        print("-" * 60)

        # Parse and structure the workflow results
        parsed_results = discovery_system._parse_group_chat_results(
            workflow_results, query
        )

        # Add the actual data we computed
        parsed_results.update(
            {
                "analysis": analysis_results,
                "discovery": discovery_results,
                "selection": selection_results,
                "assignment": assignment_results,
            }
        )

        timer.end_phase("workflow")
        workflow_time = timer.get_phase_duration("workflow")
        DetailedLogger.timing_summary("4-Agent workflow", workflow_time, "🚀")

        # Show detailed individual agent timing within workflow
        print(f"\n🔍 INDIVIDUAL AGENT TIMING BREAKDOWN:")
        print("-" * 60)
        print(
            f"🔍 Query Analysis Agent:    {query_analysis_time:.3f}s ({(query_analysis_time/workflow_time)*100:.1f}%)"
        )
        print(
            f"🔎 Discovery Agent:         {discovery_time:.3f}s ({(discovery_time/workflow_time)*100:.1f}%)"
        )
        print(
            f"🎯 Selection Agent:         {selection_time:.3f}s ({(selection_time/workflow_time)*100:.1f}%)"
        )
        print(
            f"📋 Assignment Agent:        {assignment_time:.3f}s ({(assignment_time/workflow_time)*100:.1f}%)"
        )

        # Calculate overhead (time spent between agents)
        total_agent_time = (
            query_analysis_time + discovery_time + selection_time + assignment_time
        )
        workflow_overhead = workflow_time - total_agent_time
        print(
            f"⚙️  Workflow Overhead:       {workflow_overhead:.3f}s ({(workflow_overhead/workflow_time)*100:.1f}%)"
        )
        print("-" * 60)

        # Show detailed workflow results
        workflow_details = parsed_results

        timer.start_phase("results_analysis")

        DetailedLogger.step_header(3, "Workflow Results Summary", "📊")

        # Analysis results
        analysis = workflow_details.get("analysis", {})
        print(f"🔎 Query Analysis Agent Results:")
        print(f"   Domain: {analysis.get('domain', 'N/A')}")
        print(f"   Task Summary: {analysis.get('task_summary', 'N/A')}")
        print(f"   Requirements: {', '.join(analysis.get('requirements', []))}")
        print(f"   Keywords: {', '.join(analysis.get('keywords', []))}")

        # Discovery results
        discovery = workflow_details.get("discovery", {})
        discovered_agents = discovery.get("discovered_agents", [])
        print(f"\n🔍 Discovery Agent Results:")
        print(f"   Found {len(discovered_agents)} suitable agents:")
        for i, agent in enumerate(discovered_agents, 1):
            print(
                f"   {i}. {agent.get('agent_name', 'Unknown')} (Score: {agent.get('score', 0.0)}) - {agent.get('reason', 'N/A')}"
            )

        # Selection results
        selection = workflow_details.get("selection", {})
        selected_agent = selection.get("selected_agent")
        print(f"\n🎯 Selection Agent Results:")
        if selected_agent:
            print(
                f"   Selected: {selected_agent.get('agent_name', 'Unknown')} (ID: {selected_agent.get('agent_id', 'N/A')})"
            )
            print(f"   Confidence: {selection.get('confidence', 0.0)}")
            print(f"   Reason: {selection.get('reason', 'N/A')}")
        else:
            print("   No agent was selected")

        # Assignment results
        assignment = workflow_details.get("assignment", {})
        print(f"\n📋 Assignment Agent Results:")
        if assignment.get("assignment_created"):
            assignment_details = assignment.get("assignment_details", {})
            print(f"   Assigned To: {assignment_details.get('assigned_to', 'N/A')}")
            print(f"   Priority: {assignment_details.get('priority', 'N/A')}")
            print(f"   Objective: {assignment_details.get('objective', 'N/A')}")
        else:
            print(f"   Assignment failed: {assignment.get('reason', 'Unknown error')}")

        timer.end_phase("results_analysis")
        analysis_time = timer.get_phase_duration("results_analysis")
        DetailedLogger.timing("Results analysis", analysis_time, "📊")

        # Final execution results
        DetailedLogger.step_header(4, "Specialized Agent Execution", "🚀")
        timer.start_phase("execution")

        # Step 5: Execute with specialized agent if one was selected
        if selected_agent and assignment_results.get("assignment_created"):
            timer.start_step("specialized_agent_execution")
            print(f"\n🚀 STEP 5: Specialized Agent Execution")
            print(
                f"📥 Input: Task assignment for {selected_agent.get('agent_name', 'Unknown Agent')}"
            )
            print(f"   Objective: {assignment_details.get('objective', 'N/A')}")
            print(
                f"🤖 {selected_agent.get('agent_name', 'Unknown Agent')} is executing the task..."
            )

            execution_results = await discovery_system.route_to_specialized_agent(
                parsed_results
            )

            timer.end_step("specialized_agent_execution")
            specialized_agent_time = timer.get_step_duration(
                "specialized_agent_execution"
            )

            print(f"📤 {selected_agent.get('agent_name', 'Unknown Agent')} Output:")
            if execution_results.get("success"):
                response = execution_results.get("response", "No response")
                print(
                    f"   Response: {response[:200]}{'...' if len(response) > 200 else ''}"
                )
                print(
                    f"✅ Specialized Agent execution completed in {specialized_agent_time:.3f}s"
                )

                timer.end_phase("execution")
                execution_time = timer.get_phase_duration("execution")
                DetailedLogger.timing(
                    "Specialized agent execution", execution_time, "⚡"
                )

                # Show comprehensive timing summary
                total_time = timer.get_total_time()
                print(f"\n" + "=" * 80)
                print("⏱️  DETAILED TIMING SUMMARY")
                print("=" * 80)

                print(
                    f"🏗️  Initialization Phase:     {init_time:.3f}s ({(init_time/total_time)*100:.1f}%)"
                )
                print(
                    f"🔄  4-Agent Workflow:        {workflow_time:.3f}s ({(workflow_time/total_time)*100:.1f}%)"
                )
                print(
                    f"    ├─ 🔍 Query Analysis:    {query_analysis_time:.3f}s ({(query_analysis_time/total_time)*100:.1f}%)"
                )
                print(
                    f"    ├─ 🔎 Discovery:         {discovery_time:.3f}s ({(discovery_time/total_time)*100:.1f}%)"
                )
                print(
                    f"    ├─ 🎯 Selection:         {selection_time:.3f}s ({(selection_time/total_time)*100:.1f}%)"
                )
                print(
                    f"    ├─ 📋 Assignment:        {assignment_time:.3f}s ({(assignment_time/total_time)*100:.1f}%)"
                )
                print(
                    f"    └─ ⚙️  Overhead:          {workflow_overhead:.3f}s ({(workflow_overhead/total_time)*100:.1f}%)"
                )
                print(
                    f"📊  Results Analysis:        {analysis_time:.3f}s ({(analysis_time/total_time)*100:.1f}%)"
                )
                print(
                    f"⚡  Agent Execution:         {execution_time:.3f}s ({(execution_time/total_time)*100:.1f}%)"
                )
                print(
                    f"    └─ 🤖 Specialized Agent: {specialized_agent_time:.3f}s ({(specialized_agent_time/total_time)*100:.1f}%)"
                )
                print("-" * 80)
                print(f"🎯  TOTAL OPERATION TIME:     {total_time:.3f}s")
                print(
                    f"📅  Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                )
                print("=" * 80)

                return execution_results.get("response", "No response")

            else:
                error_message = execution_results.get(
                    "response", "Unknown error occurred"
                )
                print(f"   Error: {error_message}")
                print(
                    f"❌ Specialized Agent execution failed in {specialized_agent_time:.3f}s"
                )

                timer.end_phase("execution")

                # Show timing summary even for failed execution
                total_time = timer.get_total_time()
                print(f"\n" + "=" * 80)
                print("⏱️  TIMING SUMMARY (Failed Execution)")
                print("=" * 80)
                print(f"🏗️  Initialization:          {init_time:.3f}s")
                print(f"🔄  SelectorGroupChat:       {workflow_time:.3f}s")
                print(f"❌  Failed at execution phase after: {total_time:.3f}s")
                print("=" * 80)

                if len(discovered_agents) == 0:
                    return "Agent not found"
                else:
                    return f"Error: {error_message}"
        else:
            print(f"\n❌ No agent was selected or assignment failed")
            print(f"   Cannot proceed to specialized agent execution")

            timer.end_phase("execution")

            # Show timing summary even for no execution
            total_time = timer.get_total_time()
            print(f"\n" + "=" * 80)
            print("⏱️  TIMING SUMMARY (No Agent Selected)")
            print("=" * 80)
            print(
                f"🏗️  Initialization:          {init_time:.3f}s ({(init_time/total_time)*100:.1f}%)"
            )
            print(
                f"🔄  4-Agent Workflow:        {workflow_time:.3f}s ({(workflow_time/total_time)*100:.1f}%)"
            )
            print(
                f"    ├─ 🔍 Query Analysis:    {query_analysis_time:.3f}s ({(query_analysis_time/total_time)*100:.1f}%)"
            )
            print(
                f"    ├─ 🔎 Discovery:         {discovery_time:.3f}s ({(discovery_time/total_time)*100:.1f}%)"
            )
            print(
                f"    ├─ 🎯 Selection:         {selection_time:.3f}s ({(selection_time/total_time)*100:.1f}%)"
            )
            print(
                f"    └─ 📋 Assignment:        {assignment_time:.3f}s ({(assignment_time/total_time)*100:.1f}%)"
            )
            print(
                f"📊  Results Analysis:        {analysis_time:.3f}s ({(analysis_time/total_time)*100:.1f}%)"
            )
            print(f"❌  No execution (agent not found)")
            print("-" * 80)
            print(f"🎯  TOTAL OPERATION TIME:     {total_time:.3f}s")
            print("=" * 80)

            return "Agent not found"

    except Exception as e:
        logger.error(f"Error in discovery workflow: {e}")
        DetailedLogger.error(f"Exception occurred: {e}")

        # Show timing summary even for exceptions
        total_time = timer.get_total_time()
        print(f"\n" + "=" * 80)
        print("⏱️  TIMING SUMMARY (Exception Occurred)")
        print("=" * 80)
        print(f"❌  Failed after: {total_time:.3f}s")
        print(f"📅  Failed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)

        return "Agent not found"


async def main():
    """Main function to run the discovery agent test."""

    # Check if query provided as command line argument
    if len(sys.argv) > 1:
        test_query = " ".join(sys.argv[1:])
        DetailedLogger.info(f"Using command line query: {test_query}")
    else:
        # Prompt user for query dynamically
        print("\n🔍 Discovery Agent Test - Interactive Mode (SelectorGroupChat)")
        print("=" * 60)
        print("Please enter your query for the discovery agent system:")
        print(
            "(Examples: 'Help me optimize ML models', 'Review my code for security', 'Design a user interface')"
        )
        print()

        try:
            test_query = input("Enter your query: ").strip()

            if not test_query:
                print("❌ No query provided. Exiting.")
                return

            DetailedLogger.info(f"Using interactive query: {test_query}")

        except KeyboardInterrupt:
            print("\n❌ Test cancelled by user.")
            return
        except Exception as e:
            print(f"❌ Error reading input: {e}")
            return

    result = await test_discovery_agent_workflow(test_query)

    print("\n" + "=" * 80)
    print("🎯 FINAL RESULT:")
    print("=" * 80)
    print(result)
    print("=" * 80)


if __name__ == "__main__":
    asyncio.run(main())
